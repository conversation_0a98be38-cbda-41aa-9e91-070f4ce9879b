import type { Metadata } from 'next'
import { <PERSON>, <PERSON>ser<PERSON>, <PERSON><PERSON>_<PERSON>eue } from 'next/font/google'
import './globals.css'
import Navigation from '@/components/navigation'
import Footer from '@/components/footer'
import WhatsAppWidget from '@/components/whatsapp-widget'

const inter = Inter({ subsets: ['latin'], variable: '--font-inter' })
const montserrat = Montserrat({ 
  subsets: ['latin'], 
  variable: '--font-montserrat',
  weight: ['300', '400', '500', '600', '700', '800', '900']
})
const bebasNeue = Bebas_Neue({ 
  subsets: ['latin'], 
  variable: '--font-bebas-neue',
  weight: ['400']
})

export const metadata: Metadata = {
  title: 'Del York Creative Academy',
  description: 'Premier creative education academy',
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${inter.variable} ${montserrat.variable} ${bebasNeue.variable} bg-black text-white min-h-screen`}>
        <Navigation />
        <main>{children}</main>
        <Footer />
        <WhatsAppWidget />
      </body>
    </html>
  )
}
