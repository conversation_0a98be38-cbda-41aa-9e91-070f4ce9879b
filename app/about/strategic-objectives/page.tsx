import Image from 'next/image'
import Link from 'next/link'

export default function StrategicObjectivesPage() {
  return (
    <div className="min-h-screen bg-black text-white pt-24">
      <div className="max-w-7xl mx-auto px-4">
        {/* Hero Section */}
        <section className="py-16">
          <div className="text-center mb-16">
            <h1 className="text-5xl lg:text-7xl font-montserrat font-bold mb-6">Our Strategic Objectives</h1>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Driving Africa's creative renaissance through strategic initiatives that build capacity, 
              foster innovation, and create sustainable pathways for creative professionals.
            </p>
          </div>
        </section>

        {/* Vision Statement */}
        <section className="py-16 bg-red-700 rounded-2xl mb-16">
          <div className="px-8 text-center">
            <h2 className="text-4xl font-montserrat font-bold mb-6">Our Vision</h2>
            <p className="text-2xl leading-relaxed max-w-4xl mx-auto">
              "To be Africa's foremost creative capacity building institution, 
              nurturing a generation of exceptional talents that will advance the continent 
              through film, media, and technology innovation."
            </p>
          </div>
        </section>

        {/* Strategic Pillars */}
        <section className="py-16">
          <h2 className="text-4xl font-montserrat font-bold text-center mb-16">Strategic Pillars</h2>
          
          <div className="space-y-16">
            {/* Pillar 1 */}
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-montserrat font-bold text-xl">1</span>
                  </div>
                  <h3 className="text-3xl font-montserrat font-bold">Excellence in Education</h3>
                </div>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  Deliver world-class creative education that meets international standards while 
                  celebrating African creativity and storytelling traditions.
                </p>
                <ul className="text-gray-300 space-y-3">
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Curriculum aligned with global industry standards
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Integration of African cultural narratives
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Continuous curriculum innovation and updates
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Industry-experienced faculty and mentors
                  </li>
                </ul>
              </div>
              <div className="relative">
                <Image
                  src="/placeholder.svg?height=400&width=500&text=Excellence+in+Education"
                  alt="Excellence in Education"
                  width={500}
                  height={400}
                  className="rounded-lg"
                />
              </div>
            </div>

            {/* Pillar 2 */}
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1 relative">
                <Image
                  src="/placeholder.svg?height=400&width=500&text=Industry+Integration"
                  alt="Industry Integration"
                  width={500}
                  height={400}
                  className="rounded-lg"
                />
              </div>
              <div className="order-1 lg:order-2">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-montserrat font-bold text-xl">2</span>
                  </div>
                  <h3 className="text-3xl font-montserrat font-bold">Industry Integration</h3>
                </div>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  Bridge the gap between education and industry through strategic partnerships, 
                  internships, and real-world project collaborations.
                </p>
                <ul className="text-gray-300 space-y-3">
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Strategic partnerships with leading studios
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Internship and job placement programs
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Live project collaborations with industry
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Alumni network and mentorship programs
                  </li>
                </ul>
              </div>
            </div>

            {/* Pillar 3 */}
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-montserrat font-bold text-xl">3</span>
                  </div>
                  <h3 className="text-3xl font-montserrat font-bold">Innovation & Technology</h3>
                </div>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  Embrace cutting-edge technology and innovative methodologies to prepare students 
                  for the future of creative industries.
                </p>
                <ul className="text-gray-300 space-y-3">
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    State-of-the-art equipment and facilities
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Emerging technology integration (VR, AI, etc.)
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Digital innovation labs and research
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Future-focused skill development
                  </li>
                </ul>
              </div>
              <div className="relative">
                <Image
                  src="/placeholder.svg?height=400&width=500&text=Innovation+Technology"
                  alt="Innovation & Technology"
                  width={500}
                  height={400}
                  className="rounded-lg"
                />
              </div>
            </div>

            {/* Pillar 4 */}
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1 relative">
                <Image
                  src="/placeholder.svg?height=400&width=500&text=Pan+African+Impact"
                  alt="Pan-African Impact"
                  width={500}
                  height={400}
                  className="rounded-lg"
                />
              </div>
              <div className="order-1 lg:order-2">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-montserrat font-bold text-xl">4</span>
                  </div>
                  <h3 className="text-3xl font-montserrat font-bold">Pan-African Impact</h3>
                </div>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  Expand our reach across Africa to build a continental network of creative 
                  professionals and foster cross-cultural collaboration.
                </p>
                <ul className="text-gray-300 space-y-3">
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Multi-country program expansion
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Cross-cultural creative collaborations
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    African storytelling preservation and promotion
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Continental creative industry development
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Key Performance Indicators */}
        <section className="py-16 bg-gray-900 rounded-2xl">
          <div className="px-8">
            <h2 className="text-4xl font-montserrat font-bold text-center mb-12">Key Performance Indicators</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <h3 className="text-xl font-montserrat font-bold mb-2">Graduate Employment</h3>
                <p className="text-3xl font-montserrat font-bold text-red-500 mb-2">90%</p>
                <p className="text-gray-300 text-sm">Target employment rate within 6 months</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </div>
                <h3 className="text-xl font-montserrat font-bold mb-2">Students Trained</h3>
                <p className="text-3xl font-montserrat font-bold text-red-500 mb-2">5,000</p>
                <p className="text-gray-300 text-sm">Target by 2027</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clipRule="evenodd"/>
                  </svg>
                </div>
                <h3 className="text-xl font-montserrat font-bold mb-2">African Countries</h3>
                <p className="text-3xl font-montserrat font-bold text-red-500 mb-2">20</p>
                <p className="text-gray-300 text-sm">Target presence by 2028</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                  </svg>
                </div>
                <h3 className="text-xl font-montserrat font-bold mb-2">Films Produced</h3>
                <p className="text-3xl font-montserrat font-bold text-red-500 mb-2">1,000</p>
                <p className="text-gray-300 text-sm">Target by 2027</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd"/>
                  </svg>
                </div>
                <h3 className="text-xl font-montserrat font-bold mb-2">Industry Partners</h3>
                <p className="text-3xl font-montserrat font-bold text-red-500 mb-2">150</p>
                <p className="text-gray-300 text-sm">Target partnerships by 2026</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd"/>
                  </svg>
                </div>
                <h3 className="text-xl font-montserrat font-bold mb-2">Innovation Projects</h3>
                <p className="text-3xl font-montserrat font-bold text-red-500 mb-2">50</p>
                <p className="text-gray-300 text-sm">Target research initiatives by 2026</p>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 text-center">
          <h2 className="text-4xl font-montserrat font-bold mb-6">Join Our Strategic Mission</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Be part of the strategic transformation of Africa's creative landscape. 
            Together, we're building the future of African creativity and innovation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/register"
              className="bg-red-600 text-white px-8 py-3 text-lg font-bebas tracking-wider hover:bg-red-700 transition-colors"
            >
              JOIN THE MISSION
            </Link>
            <Link
              href="/about/founder"
              className="border border-white text-white px-8 py-3 text-lg font-bebas tracking-wider hover:bg-white hover:text-black transition-colors"
            >
              MEET OUR FOUNDER
            </Link>
          </div>
        </section>
      </div>
    </div>
  )
}
