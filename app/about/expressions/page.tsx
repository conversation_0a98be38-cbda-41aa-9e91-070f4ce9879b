import Image from 'next/image'
import Link from 'next/link'

export default function ExpressionsPage() {
  return (
    <div className="min-h-screen bg-black text-white pt-24">
      <div className="max-w-7xl mx-auto px-4">
        {/* Hero Section */}
        <section className="py-16">
          <div className="text-center mb-16">
            <h1 className="text-5xl lg:text-7xl font-montserrat font-bold mb-6">Our Expressions</h1>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Discover how Del York Creative Academy manifests its vision through diverse creative expressions, 
              innovative programs, and transformative educational experiences that celebrate African creativity.
            </p>
          </div>
        </section>

        {/* Creative Philosophy */}
        <section className="py-16 bg-red-700 rounded-2xl mb-16">
          <div className="px-8 text-center">
            <h2 className="text-4xl font-montserrat font-bold mb-6">Our Creative Philosophy</h2>
            <blockquote className="text-2xl leading-relaxed max-w-4xl mx-auto mb-8">
              "We believe that creativity is the universal language that transcends boundaries, 
              connects cultures, and transforms lives. Our expressions are rooted in African 
              authenticity while embracing global excellence."
            </blockquote>
            <div className="grid md:grid-cols-3 gap-8 mt-12">
              <div>
                <h3 className="text-xl font-montserrat font-bold mb-3">Authenticity</h3>
                <p>Celebrating African stories, traditions, and perspectives</p>
              </div>
              <div>
                <h3 className="text-xl font-montserrat font-bold mb-3">Innovation</h3>
                <p>Embracing cutting-edge technology and creative methodologies</p>
              </div>
              <div>
                <h3 className="text-xl font-montserrat font-bold mb-3">Excellence</h3>
                <p>Maintaining world-class standards in all our endeavors</p>
              </div>
            </div>
          </div>
        </section>

        {/* Expression Categories */}
        <section className="py-16">
          <h2 className="text-4xl font-montserrat font-bold text-center mb-16">How We Express Our Vision</h2>
          
          <div className="space-y-20">
            {/* Educational Innovation */}
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl font-montserrat font-bold mb-6 text-red-500">Educational Innovation</h3>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  We express our commitment to excellence through innovative educational approaches 
                  that blend traditional African storytelling with modern filmmaking techniques.
                </p>
                <ul className="text-gray-300 space-y-3">
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Immersive learning environments that simulate real industry conditions
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Integration of African cultural narratives in curriculum design
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Collaborative projects that celebrate diversity and inclusion
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Mentorship programs connecting students with industry leaders
                  </li>
                </ul>
              </div>
              <div className="relative">
                <Image
                  src="/placeholder.svg?height=400&width=500&text=Educational+Innovation"
                  alt="Educational Innovation"
                  width={500}
                  height={400}
                  className="rounded-lg"
                />
                <div className="absolute -bottom-4 -right-4 bg-red-600 text-white p-3 rounded-lg">
                  <p className="font-bebas tracking-wider">INNOVATION</p>
                </div>
              </div>
            </div>

            {/* Cultural Preservation */}
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1 relative">
                <Image
                  src="/placeholder.svg?height=400&width=500&text=Cultural+Preservation"
                  alt="Cultural Preservation"
                  width={500}
                  height={400}
                  className="rounded-lg"
                />
                <div className="absolute -bottom-4 -left-4 bg-red-600 text-white p-3 rounded-lg">
                  <p className="font-bebas tracking-wider">HERITAGE</p>
                </div>
              </div>
              <div className="order-1 lg:order-2">
                <h3 className="text-3xl font-montserrat font-bold mb-6 text-red-500">Cultural Preservation</h3>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  Our expressions honor and preserve African cultural heritage while adapting 
                  traditional storytelling for contemporary global audiences.
                </p>
                <ul className="text-gray-300 space-y-3">
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Documentation and digitization of traditional African stories
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Integration of indigenous languages in film and media projects
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Collaboration with cultural custodians and traditional leaders
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Modern interpretation of ancient African artistic traditions
                  </li>
                </ul>
              </div>
            </div>

            {/* Global Collaboration */}
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl font-montserrat font-bold mb-6 text-red-500">Global Collaboration</h3>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  We express our vision through international partnerships that create bridges 
                  between African creativity and global opportunities.
                </p>
                <ul className="text-gray-300 space-y-3">
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Exchange programs with international film schools and studios
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Co-production opportunities with global entertainment companies
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    International film festival participation and recognition
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Cross-cultural creative collaborations and joint ventures
                  </li>
                </ul>
              </div>
              <div className="relative">
                <Image
                  src="/placeholder.svg?height=400&width=500&text=Global+Collaboration"
                  alt="Global Collaboration"
                  width={500}
                  height={400}
                  className="rounded-lg"
                />
                <div className="absolute -bottom-4 -right-4 bg-red-600 text-white p-3 rounded-lg">
                  <p className="font-bebas tracking-wider">GLOBAL</p>
                </div>
              </div>
            </div>

            {/* Community Impact */}
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1 relative">
                <Image
                  src="/placeholder.svg?height=400&width=500&text=Community+Impact"
                  alt="Community Impact"
                  width={500}
                  height={400}
                  className="rounded-lg"
                />
                <div className="absolute -bottom-4 -left-4 bg-red-600 text-white p-3 rounded-lg">
                  <p className="font-bebas tracking-wider">IMPACT</p>
                </div>
              </div>
              <div className="order-1 lg:order-2">
                <h3 className="text-3xl font-montserrat font-bold mb-6 text-red-500">Community Impact</h3>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  Our expressions extend beyond the classroom to create meaningful impact 
                  in communities across Africa through outreach and development programs.
                </p>
                <ul className="text-gray-300 space-y-3">
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Community-based film projects addressing local issues
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Free workshops and training programs for underserved communities
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Youth empowerment initiatives through creative arts
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3">•</span>
                    Scholarship programs for talented students from disadvantaged backgrounds
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Creative Showcase */}
        <section className="py-16 bg-gray-900 rounded-2xl">
          <div className="px-8">
            <h2 className="text-4xl font-montserrat font-bold text-center mb-12">Our Creative Showcase</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="bg-black rounded-lg overflow-hidden">
                <Image
                  src="/placeholder.svg?height=200&width=300&text=Student+Film+Festival"
                  alt="Student Film Festival"
                  width={300}
                  height={200}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-montserrat font-bold mb-3">Annual Film Festival</h3>
                  <p className="text-gray-300 mb-4">
                    Celebrating student creativity through our annual showcase of outstanding films and projects.
                  </p>
                  <Link href="/events" className="text-red-500 hover:text-red-400 font-bebas tracking-wider">
                    LEARN MORE →
                  </Link>
                </div>
              </div>

              <div className="bg-black rounded-lg overflow-hidden">
                <Image
                  src="/placeholder.svg?height=200&width=300&text=Cultural+Exchange"
                  alt="Cultural Exchange Program"
                  width={300}
                  height={200}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-montserrat font-bold mb-3">Cultural Exchange</h3>
                  <p className="text-gray-300 mb-4">
                    International programs that foster cross-cultural understanding and collaboration.
                  </p>
                  <Link href="/special-programs" className="text-red-500 hover:text-red-400 font-bebas tracking-wider">
                    EXPLORE →
                  </Link>
                </div>
              </div>

              <div className="bg-black rounded-lg overflow-hidden">
                <Image
                  src="/placeholder.svg?height=200&width=300&text=Innovation+Lab"
                  alt="Innovation Lab"
                  width={300}
                  height={200}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-montserrat font-bold mb-3">Innovation Lab</h3>
                  <p className="text-gray-300 mb-4">
                    Cutting-edge research and development in creative technologies and methodologies.
                  </p>
                  <Link href="/about/what-we-do" className="text-red-500 hover:text-red-400 font-bebas tracking-wider">
                    DISCOVER →
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Values in Action */}
        <section className="py-16">
          <h2 className="text-4xl font-montserrat font-bold text-center mb-12">Our Values in Action</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd"/>
                </svg>
              </div>
              <h3 className="text-xl font-montserrat font-bold mb-3">Passion</h3>
              <p className="text-gray-300">
                We approach every project with genuine passion for African creativity and storytelling excellence.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                </svg>
              </div>
              <h3 className="text-xl font-montserrat font-bold mb-3">Integrity</h3>
              <p className="text-gray-300">
                We maintain the highest standards of honesty, transparency, and ethical practice in all our expressions.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                </svg>
              </div>
              <h3 className="text-xl font-montserrat font-bold mb-3">Collaboration</h3>
              <p className="text-gray-300">
                We believe in the power of collaboration to create extraordinary results and lasting impact.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd"/>
                </svg>
              </div>
              <h3 className="text-xl font-montserrat font-bold mb-3">Innovation</h3>
              <p className="text-gray-300">
                We continuously push boundaries and explore new ways to express creativity and deliver education.
              </p>
            </div>
          </div>
        </section>

        {/* Future Expressions */}
        <section className="py-16 bg-red-700 rounded-2xl">
          <div className="px-8 text-center">
            <h2 className="text-4xl font-montserrat font-bold mb-6">Future Expressions</h2>
            <p className="text-xl leading-relaxed max-w-4xl mx-auto mb-8">
              As we continue to evolve, our expressions will expand to embrace new technologies, 
              reach more communities, and create even greater impact across the African continent and beyond.
            </p>
            <div className="grid md:grid-cols-3 gap-8 mt-12">
              <div>
                <h3 className="text-xl font-montserrat font-bold mb-3">Virtual Reality</h3>
                <p>Immersive storytelling experiences that transport audiences into African narratives</p>
              </div>
              <div>
                <h3 className="text-xl font-montserrat font-bold mb-3">AI Integration</h3>
                <p>Leveraging artificial intelligence to enhance creative processes and learning outcomes</p>
              </div>
              <div>
                <h3 className="text-xl font-montserrat font-bold mb-3">Global Expansion</h3>
                <p>Extending our reach to African diaspora communities worldwide</p>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 text-center">
          <h2 className="text-4xl font-montserrat font-bold mb-6">Express Yourself With Us</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Join our community of creative expressionists who are redefining African storytelling 
            and making their mark on the global creative landscape. Your unique voice and vision 
            are the next chapter in our collective expression.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/register"
              className="bg-red-600 text-white px-8 py-3 text-lg font-bebas tracking-wider hover:bg-red-700 transition-colors"
            >
              JOIN OUR EXPRESSION
            </Link>
            <Link
              href="/gallery"
              className="border border-white text-white px-8 py-3 text-lg font-bebas tracking-wider hover:bg-white hover:text-black transition-colors"
            >
              VIEW OUR GALLERY
            </Link>
          </div>
        </section>
      </div>
    </div>
  )
}
