import Image from 'next/image'

export default function NewsPage() {
  const news = [
    {
      title: "Del York Creative Academy Announces New Campus",
      date: "December 15, 2024",
      excerpt: "We're expanding our reach with a new state-of-the-art facility...",
      image: "/placeholder.svg?height=200&width=300"
    },
    {
      title: "Alumni Wins International Film Award",
      date: "December 10, 2024",
      excerpt: "Former student takes home prestigious award at Cannes...",
      image: "/placeholder.svg?height=200&width=300"
    },
    {
      title: "New Partnership with Netflix Announced",
      date: "December 5, 2024",
      excerpt: "Strategic partnership will provide more opportunities for students...",
      image: "/placeholder.svg?height=200&width=300"
    }
  ]

  return (
    <div className="min-h-screen bg-black text-white pt-24">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-5xl font-bold mb-8">Latest News</h1>
        
        <div className="space-y-8">
          {news.map((article, index) => (
            <article key={index} className="bg-gray-900 rounded-lg overflow-hidden md:flex">
              <Image
                src={article.image || "/placeholder.svg"}
                alt={article.title}
                width={300}
                height={200}
                className="w-full md:w-80 h-48 object-cover"
              />
              <div className="p-6 flex-1">
                <div className="text-sm text-gray-400 mb-2">{article.date}</div>
                <h2 className="text-2xl font-bold mb-4">{article.title}</h2>
                <p className="text-gray-300">{article.excerpt}</p>
              </div>
            </article>
          ))}
        </div>
      </div>
    </div>
  )
}
