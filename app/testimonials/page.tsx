import Image from 'next/image'
import { Star } from 'lucide-react'

export default function TestimonialsPage() {
  const testimonials = [
    {
      name: "<PERSON>",
      program: "Professional Filmmaking",
      year: "Class of 2023",
      image: "/placeholder.svg?height=100&width=100",
      rating: 5,
      testimonial: "Del York Creative Academy transformed my passion into a profession. The hands-on training and industry connections I gained here were invaluable. I'm now working as a director for a major production company."
    },
    {
      name: "<PERSON>",
      program: "Acting & Performance",
      year: "Class of 2022",
      image: "/placeholder.svg?height=100&width=100",
      rating: 5,
      testimonial: "The acting program at DCA pushed me beyond my comfort zone and helped me discover my true potential. The instructors are industry professionals who genuinely care about your growth."
    },
    {
      name: "<PERSON><PERSON>",
      program: "Digital Media",
      year: "Class of 2024",
      image: "/placeholder.svg?height=100&width=100",
      rating: 5,
      testimonial: "As someone who was new to digital media, the comprehensive curriculum and supportive environment at DCA gave me the confidence and skills to launch my own content creation business."
    },
    {
      name: "<PERSON>",
      program: "Screenwriting",
      year: "Class of 2023",
      image: "/placeholder.svg?height=100&width=100",
      rating: 5,
      testimonial: "The screenwriting program exceeded my expectations. I learned not just the craft of writing, but also the business side of the industry. My screenplay is now in pre-production!"
    },
    {
      name: "Amara Okafor",
      program: "Cinematography",
      year: "Class of 2022",
      image: "/placeholder.svg?height=100&width=100",
      rating: 5,
      testimonial: "DCA provided me with access to professional-grade equipment and mentorship from industry veterans. The practical experience I gained here was instrumental in landing my dream job."
    },
    {
      name: "James Rodriguez",
      program: "Post-Production",
      year: "Class of 2024",
      image: "/placeholder.svg?height=100&width=100",
      rating: 5,
      testimonial: "The post-production program at DCA is world-class. The combination of technical training and creative development prepared me for the fast-paced demands of the industry."
    }
  ]

  return (
    <div className="min-h-screen bg-black text-white pt-24">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold mb-4">Student Testimonials</h1>
          <p className="text-xl text-gray-300">
            Hear from our graduates about their transformative experience at Del York Creative Academy
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-gray-900 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <Image
                  src={testimonial.image || "/placeholder.svg"}
                  alt={testimonial.name}
                  width={60}
                  height={60}
                  className="rounded-full mr-4"
                />
                <div>
                  <h3 className="font-bold">{testimonial.name}</h3>
                  <p className="text-sm text-gray-400">{testimonial.program}</p>
                  <p className="text-sm text-gray-400">{testimonial.year}</p>
                </div>
              </div>
              
              <div className="flex mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                ))}
              </div>
              
              <p className="text-gray-300 italic">"{testimonial.testimonial}"</p>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center bg-gray-900 rounded-lg p-8">
          <h2 className="text-3xl font-bold mb-4">Ready to Start Your Journey?</h2>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            Join hundreds of successful graduates who have transformed their creative passion into thriving careers. 
            Your success story could be next.
          </p>
          <a
            href="/register"
            className="inline-block bg-white text-black px-8 py-3 text-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            APPLY NOW
          </a>
        </div>
      </div>
    </div>
  )
}
