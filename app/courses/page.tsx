import Image from 'next/image'
import Link from 'next/link'
import { title } from 'process'

export default function CoursesPage() {
  const courses = [
    {
      title: "Post Production",
      duration: "8 weeks",
      description: "The Final Cut: Mastering the Art of Post Production for Film and TV",
      image: "/images/online-courses/dca-website-banner-post-prod.jpg",
      type: 'online'
    },
    {
      title: "Fashion, Art & Crafts",
      duration: "6 weeks",
      description: "Unleashing Your Creativity: Exploring the World of Fashion, Art & Crafts",
      image: "/images/online-courses/dca-website-banner-fashion.jpg",
      type: 'online'
    },
    {
      title: "Digital Film Making",
      duration: "12 weeks",
      description: "Bringing Your Vision to Life: The Fundamentals of Digital Film making",
      image: "/images/online-courses/dca-website-banner-digital-film.jpg",
      type: 'online'
    },
    {
      title: "Documentary Film-making",
      duration: "10 weeks",
      description: "Telling True Stories: The Art of Documentary Film-making",
      image: "/images/online-courses/dca-website-banner-documentary.jpg",
      type: 'online'
    },
    {
      title: "Digital Marketing",
      duration: "8 weeks",
      description: "Transform Your Online Presence: Mastering the Fundamentals of Digital Marketing",
      image: "/images/online-courses/dca-website-banner-digital-marketing.jpg",
      type: 'online'
    },
    {
      title: "Producing And The Business Of Film & TV",
      duration: "12 weeks",
      description: "From Script to Screen: Mastering the Art of Film and TV Production",
      image: "/images/online-courses/prod-business-of-film.jpg",
      type: 'online'
    },

    // On premise courses
    {
      title: "Digital Marketing/Content Creation",
      duration: "12 weeks",
      description: "Unleash Your Creativity: Mastering the Art of Graphic Design",
      image: "/images/on-premise/dca-Digital-Content-Creation-And-Social-Media.jpg",
      type: 'on-premise'
    },
    {
      title: "Filmmaking/Directing",
      duration: "12 weeks",
      description: "From Concept to Screen: Mastering the Art of Filmmaking and Directing",
      image: "/images/on-premise/dca-directing.jpg",
      type: 'on-premise'
    },
    {
      title: "Post production/ Editing",
      duration: "12 weeks",
      description: "The Final Cut: Mastering the Art of Post Production for Film and TV",
      image: "/images/on-premise/dca-film-editing-1.jpg",
      type: 'on-premise'
    },
    {
      title: "Fashion, Art & Craft",
      duration: "12 weeks",
      description: "Unleashing Your Creativity: Exploring the World of Fashion, Art & Crafts",
      image: "/images/on-premise/dca-delyork-fashion-art-and-craft-course.jpg",
      type: 'on-premise'
    },
    {
      title: "Public Relations & Media Communications",
      duration: "12 weeks",
      description: "Mastering the Art of Public Relations & Media Communications",
      image: "/images/on-premise/public-relations-course.jpg",
      type: 'on-premise'
    },
    {
      title: "Screenwriting For Film & TV",
      duration: "12 weeks",
      description: "From Concept to Screen: Mastering the Art of Screenwriting for Film and TV",
      image: "/images/on-premise/screen-writing-1.jpg",
      type: 'on-premise'
    },
    {
      title: "Acting For Film",
      duration: "12 weeks",
      description: "From Concept to Screen: Mastering the Art of Screenwriting for Film and TV",
      image: "/images/on-premise/acting-for-film.jpg",
      type: 'on-premise'
    },
    {
      title: "Photography",
      duration: "12 weeks",
      description: "Capturing the Moment: Mastering the Art of Photography",
      image: "/images/on-premise/photography-course.jpg",
      type: 'on-premise'
    }, 
    {
      title: "Digital Marketing",
      duration: "12 weeks",
      description: "Transform Your Online Presence: Mastering the Fundamentals of Digital Marketing",
      image: "/images/on-premise/digital-marketing-new.jpg",
      type: 'on-premise'
    },
    {
      title: "Sound Design & Scoring For Film",
      duration: "12 weeks",
      description: "From Concept to Screen: Mastering the Art of Screenwriting for Film and TV",
      image: "/images/on-premise/sound-design.jpg",
      type: 'on-premise'
    },
    {
      title: "Costume & Set Design",
      duration: "12 weeks",
      description: "From Concept to Screen: Mastering the Art of Screenwriting for Film and TV",
      image: "/images/on-premise/set-design-course.jpg",
      type: 'on-premise'
    },
    {
      title: "Music Video Production",
      duration: "12 weeks",
      description: "From Concept to Screen: Mastering the Art of Screenwriting for Film and TV",
      image: "/images/on-premise/music-video-production.jpg",
      type: 'on-premise'
    },
    {
      title: "Post Production/ Editing",
      duration: "12 weeks",
      description: "From Concept to Screen: Mastering the Art of Screenwriting for Film and TV",
      image: "/images/on-premise/film-editing-1.jpg",
      type: 'on-premise'
    },
    {
      title: "Drones",
      duration: "12 weeks",
      description: "From Concept to Screen: Mastering the Art of Screenwriting for Film and TV",
      image: "/images/on-premise/drone-course.jpg",
      type: 'on-premise'
    },
    {
      title: "Filmmaking/Directing",
      duration: "12 weeks",
      description: "From Concept to Screen: Mastering the Art of Screenwriting for Film and TV",
      image: "/images/on-premise/directing.jpg",
      type: 'on-premise'
    },
    {
      title: "Vfx",
      duration: "12 weeks",
      description: "From Concept to Screen: Mastering the Art of Screenwriting for Film and TV",
      image: "/images/on-premise/vfx.jpg",
      type: 'on-premise'
    }
  ]

  return (
    <div className="min-h-screen bg-black text-white py-24">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-5xl font-bold mb-8">Our Courses</h1>
        <p className="text-xl text-gray-300 mb-12">
Our comprehensive training programs equip you with the skills and expertise needed to succeed in the creative industry. Gain practical experience, build a portfolio, and stay ahead of the curve with industry-leading curriculum. Launch your career with confidence and achieve your goals in graphic design, digital media, photography, and more.
        </p>

 

        <h2 className="text-3xl font-bold mb-8">Online <span className='text-brand'>Courses</span></h2>
        <div className="grid md:grid-cols-3 gap-8">
          {courses.filter(course => course.type === 'online').map((course, index) => (
            <div key={index} className="bg-gray-900 rounded-lg overflow-hidden">
              <Image
                src={course.image || "/placeholder.svg"}
                alt={course.title}
                width={400}
                height={300}
                className="w-full h-64  object-cover"
              />
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-lg font-bold">{course.title}</h3>
                  <span className="text-sm bg-gray-800 px-3 py-1 rounded whitespace-nowrap">{course.duration}</span>
                </div>
                <p className="text-gray-300 mb-4">{course.description}</p>
                <Link 
                  href="https://portal.delyorkcreative.academy/student/apply" 
                  className="inline-block bg-white text-black px-6 py-2 font-semibold hover:bg-gray-100 transition-colors"
                >
                  Enroll Now
                </Link>
              </div>
            </div>
          ))}
        </div>
            <br /> <br />
        <h2 className="text-3xl font-bold my-8">On-premise <span className='text-brand'>Courses</span></h2>
        <div className="grid md:grid-cols-3 gap-8">
          {courses.filter(course => course.type === 'on-premise').map((course, index) => (
            <div key={index} className="bg-gray-900 rounded-lg overflow-hidden">
              <Image
                src={course.image || "/placeholder.svg"}
                alt={course.title}
                width={400}
                height={250}
                className="w-full h-64  object-cover"
              />
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-lg font-bold">{course.title}</h3>
                  <span className="text-sm bg-gray-800 px-3 py-1 rounded whitespace-nowrap">{course.duration}</span>
                </div>
                <p className="text-gray-300 mb-4">{course.description}</p>
                <Link 
                  href="https://portal.delyorkcreative.academy/student/apply" 
                  className="inline-block bg-white text-black px-6 py-2 font-semibold hover:bg-gray-100 transition-colors"
                >
                  Enroll Now
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
