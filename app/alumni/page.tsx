import Image from 'next/image'

export default function AlumniPage() {
  const alumni = [
    {
      name: "<PERSON>",
      year: "Class of 2023",
      achievement: "Director of Netflix Original Series",
      image: "/placeholder.svg?height=200&width=200"
    },
    {
      name: "<PERSON>",
      year: "Class of 2022",
      achievement: "Lead Actor in Hollywood Blockbuster",
      image: "/placeholder.svg?height=200&width=200"
    },
    {
      name: "<PERSON>",
      year: "Class of 2024",
      achievement: "Founder of Successful Production Company",
      image: "/placeholder.svg?height=200&width=200"
    }
  ]

  return (
    <div className="min-h-screen bg-black text-white pt-24">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-5xl font-bold mb-8">Our Alumni</h1>
        <p className="text-xl text-gray-300 mb-12">
          Celebrating the success stories of our graduates making waves in the industry.
        </p>

        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {alumni.map((person, index) => (
            <div key={index} className="bg-gray-900 rounded-lg p-6 text-center">
              <Image
                src={person.image || "/placeholder.svg"}
                alt={person.name}
                width={200}
                height={200}
                className="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
              />
              <h3 className="text-xl font-bold mb-2">{person.name}</h3>
              <p className="text-gray-400 mb-2">{person.year}</p>
              <p className="text-gray-300">{person.achievement}</p>
            </div>
          ))}
        </div>

        <section className="text-center">
          <h2 className="text-3xl font-bold mb-6">Join Our Alumni Network</h2>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Connect with fellow graduates, share opportunities, and continue growing together 
            as part of the Del York Creative Academy family.
          </p>
        </section>
      </div>
    </div>
  )
}
