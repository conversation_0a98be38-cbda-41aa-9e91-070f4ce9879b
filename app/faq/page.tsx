"use client"

import { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'

export default function FAQPage() {
  const [openItems, setOpenItems] = useState<number[]>([])

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    )
  }

  const faqs = [
    {
      category: "Admissions",
      questions: [
        {
          question: "What are the admission requirements?",
          answer: "Admission requirements vary by program. Generally, you need a high school diploma or equivalent, a portfolio (for creative programs), and completion of our application process including an interview."
        },
        {
          question: "When do applications open?",
          answer: "Applications typically open in January for the August intake and in June for the January intake. We recommend applying early as spaces are limited."
        },
        {
          question: "Is there an age limit for enrollment?",
          answer: "There is no upper age limit. Our minimum age requirement is 16 years old, though some programs may require students to be 18 or older."
        }
      ]
    },
    {
      category: "Programs & Courses",
      questions: [
        {
          question: "How long are the programs?",
          answer: "Program duration varies: Certificate programs are 6-12 weeks, Diploma programs are 6-12 months, and Degree programs are 2-4 years."
        },
        {
          question: "Are classes available part-time?",
          answer: "Yes, we offer both full-time and part-time options for most programs. Part-time classes are typically held in the evenings and weekends."
        },
        {
          question: "Do you offer online courses?",
          answer: "Yes, we have a selection of online courses and hybrid programs that combine online learning with hands-on practical sessions."
        }
      ]
    },
    {
      category: "Financial",
      questions: [
        {
          question: "What are the tuition fees?",
          answer: "Tuition fees vary by program. Certificate programs start from ₦150,000, while diploma programs range from ₦500,000 to ₦1,200,000. Contact our admissions office for detailed fee structures."
        },
        {
          question: "Do you offer scholarships?",
          answer: "Yes, we offer merit-based scholarships, need-based financial aid, and industry-sponsored scholarships. Applications are reviewed on a case-by-case basis."
        },
        {
          question: "Can I pay in installments?",
          answer: "Yes, we offer flexible payment plans that allow you to pay tuition in installments throughout the program duration."
        }
      ]
    },
    {
      category: "Career Services",
      questions: [
        {
          question: "Do you help with job placement?",
          answer: "Yes, we have a dedicated career services team that helps students with job placement, internships, portfolio development, and industry networking."
        },
        {
          question: "What is the employment rate of graduates?",
          answer: "Over 85% of our graduates find employment in their field within 6 months of graduation, with many securing positions before completing their programs."
        }
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-black text-white pt-24">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold mb-4">Frequently Asked Questions</h1>
          <p className="text-xl text-gray-300">
            Find answers to common questions about our programs, admissions, and services
          </p>
        </div>

        <div className="space-y-8">
          {faqs.map((category, categoryIndex) => (
            <div key={categoryIndex}>
              <h2 className="text-2xl font-bold mb-4 text-center">{category.category}</h2>
              <div className="space-y-4">
                {category.questions.map((faq, questionIndex) => {
                  const itemIndex = categoryIndex * 100 + questionIndex
                  const isOpen = openItems.includes(itemIndex)
                  
                  return (
                    <div key={questionIndex} className="bg-gray-900 rounded-lg">
                      <button
                        onClick={() => toggleItem(itemIndex)}
                        className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-800 transition-colors"
                      >
                        <span className="font-semibold">{faq.question}</span>
                        {isOpen ? (
                          <ChevronUp className="h-5 w-5 text-gray-400" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                      {isOpen && (
                        <div className="px-6 pb-4">
                          <p className="text-gray-300">{faq.answer}</p>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center bg-gray-900 rounded-lg p-8">
          <h3 className="text-2xl font-bold mb-4">Still have questions?</h3>
          <p className="text-gray-300 mb-6">
            Can't find the answer you're looking for? Our admissions team is here to help.
          </p>
          <a
            href="/contact"
            className="inline-block bg-white text-black px-8 py-3 text-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            CONTACT US
          </a>
        </div>
      </div>
    </div>
  )
}
