"use client"

import Link from 'next/link'
import Image from 'next/image'
import { useState } from 'react'
import { ChevronDown, Menu, X, ChevronRight } from 'lucide-react'

export default function Navigation() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [showMoreCourses, setShowMoreCourses] = useState(false)
  const [showFilmMediaCourses, setShowFilmMediaCourses] = useState(false)

  return (
    <nav className="fixed w-full top-0 z-50 bg-black/90 backdrop-blur-sm border-b border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left Menu */}
          <div className="hidden lg:flex items-center space-x-8">
            <div className="relative group">
              <button className="text-white hover:text-gray-300 flex items-center space-x-1">
                <span>About</span>
                <ChevronDown className="h-4 w-4" />
              </button>
              <div className="absolute top-full left-0 mt-2 w-56 bg-red-700 border border-red-600 rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                <Link href="/about/what-we-do" className="block px-4 py-3 text-white hover:bg-red-600 font-montserrat">What We Do</Link>
                <Link href="/about/strategic-objectives" className="block px-4 py-3 text-white hover:bg-red-600 font-montserrat">Our Strategic Objectives</Link>
                <Link href="/about/founder" className="block px-4 py-3 text-white hover:bg-red-600 font-montserrat">Founder</Link>
                <Link href="/about/expressions" className="block px-4 py-3 text-white hover:bg-red-600 font-montserrat">Our Expressions</Link>
              </div>
            </div>
            
            <div className="relative group">
              <button className="text-white hover:text-gray-300 flex items-center space-x-1">
                <span>Special Programs</span>
                <ChevronDown className="h-4 w-4" />
              </button>
              <div className="absolute top-full left-0 mt-2 w-56 bg-black border border-gray-800 rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                <Link href="/special-programs" className="block px-4 py-2 text-sm hover:bg-gray-800">All Programs</Link>
                <Link href="/special-programs/masterclass" className="block px-4 py-2 text-sm hover:bg-gray-800">Masterclass Series</Link>
                <Link href="/special-programs/immersion" className="block px-4 py-2 text-sm hover:bg-gray-800">Industry Immersion</Link>
                <Link href="/special-programs/workshops" className="block px-4 py-2 text-sm hover:bg-gray-800">Weekend Workshops</Link>
                <Link href="/special-programs/summer" className="block px-4 py-2 text-sm hover:bg-gray-800">Summer Intensive</Link>
                <Link href="/special-programs/online" className="block px-4 py-2 text-sm hover:bg-gray-800">Online Programs</Link>
              </div>
            </div>
            
            <div className="relative group">
              <button className="text-white hover:text-gray-300 flex items-center space-x-1">
                <span>Courses</span>
                <ChevronDown className="h-4 w-4" />
              </button>
              
              {/* Mega Menu Dropdown */}
              <div className="absolute top-full left-0 mt-2 w-[800px] bg-red-700 border border-red-600 rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                <div className="grid grid-cols-12 min-h-[400px]">
                  {/* Left Sidebar - Categories */}
                  <div className="col-span-4 bg-red-800 p-6 rounded-l-md">
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-white font-montserrat font-bold text-lg mb-4">Course Categories</h3>
                      </div>
                      
                      <div className="relative">
                        <button 
                          className="text-white font-montserrat font-semibold text-base mb-2 flex items-center justify-between w-full hover:text-red-200 transition-colors"
                          onMouseEnter={() => setShowFilmMediaCourses(true)}
                          onMouseLeave={() => setShowFilmMediaCourses(false)}
                        >
                          <span>Film & Media Courses</span>
                          <ChevronRight className="h-4 w-4" />
                        </button>
                        
                        {/* Film & Media Courses Submenu */}
                        {showFilmMediaCourses && (
                          <div 
                            className="absolute left-full top-0 ml-2 w-80 bg-red-700 border border-red-600 rounded-md p-4 z-60"
                            onMouseEnter={() => setShowFilmMediaCourses(true)}
                            onMouseLeave={() => setShowFilmMediaCourses(false)}
                          >
                            <div className="space-y-3">
                              <Link href="/courses/acting-for-film" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Acting For Film (On-Premise)
                              </Link>
                              
                              <Link href="/courses/screenwriting" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Screenwriting For Film & TV (On-Premise)
                              </Link>
                              
                              <Link href="/courses/filmmaking" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Filmmaking/Directing
                              </Link>
                              
                              <Link href="/courses/post-production" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Post Production/Editing
                              </Link>
                              
                              <Link href="/courses/digital-content" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Digital Content Creation & Social Media Influencing
                              </Link>
                              
                              <Link href="/courses/public-relations" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Public Relations & Media Communications
                              </Link>
                              
                              <Link href="/courses/3d-animation" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                3D Animation
                              </Link>
                              
                              <Link href="/courses/producing" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Producing & The Business of Film-Making
                              </Link>
                              
                              <Link href="/courses/fashion-arts" className="block text-white hover:text-red-200 font-montserrat text-base py-2 hover:border-red-200/50 transition-colors">
                                Fashion, Arts & Craft
                              </Link>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      <div className="relative">
                        <button 
                          className="text-white font-montserrat font-semibold text-base mb-2 flex items-center justify-between w-full hover:text-red-200 transition-colors"
                          onMouseEnter={() => setShowMoreCourses(true)}
                          onMouseLeave={() => setShowMoreCourses(false)}
                        >
                          <span>More</span>
                          <ChevronRight className="h-4 w-4" />
                        </button>
                        
                        {/* More Courses Submenu */}
                        {showMoreCourses && (
                          <div 
                            className="absolute left-full top-0 ml-2 w-80 bg-red-700 border border-red-600 rounded-md p-4 z-60"
                            onMouseEnter={() => setShowMoreCourses(true)}
                            onMouseLeave={() => setShowMoreCourses(false)}
                          >
                            <div className="space-y-3">
                              <Link href="/courses/music-video-production" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Music Video Production
                              </Link>
                              
                              <Link href="/courses/vfx" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                VFX
                              </Link>
                              
                              <Link href="/courses/cinematography-lighting" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Cinematography & Set Lighting
                              </Link>
                              
                              <Link href="/courses/theater-production" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Intro To Theater Production
                              </Link>
                              
                              <Link href="/courses/makeup-and-special-effects" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Makeup & Special Effects
                              </Link>
                              
                              <Link href="/courses/costume-set-design" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Costume & Set Design
                              </Link>
                              
                              <Link href="/courses/digital-marketing" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Digital Marketing
                              </Link>
                              
                              <Link href="/courses/photography" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Photography
                              </Link>
                              
                              <Link href="/courses/colouring-grading" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Colouring & Grading
                              </Link>
                              
                              <Link href="/courses/drones" className="block text-white hover:text-red-200 font-montserrat text-base py-2 border-b border-red-600/30 hover:border-red-200/50 transition-colors">
                                Drones
                              </Link>
                              
                              <Link href="/courses/sound-design-scoring" className="block text-white hover:text-red-200 font-montserrat text-base py-2 hover:border-red-200/50 transition-colors">
                                Sound Design & Scoring For Film
                              </Link>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      <div>
                        <h4 className="text-white font-montserrat font-semibold text-base mb-2">Online Courses</h4>
                        <Link href="/courses" className="text-red-200 hover:text-white text-sm block">
                          View All Online Programs
                        </Link>
                      </div>
                    </div>
                  </div>
                  
                  {/* Right Content - Featured Courses */}
                  <div className="col-span-8 p-6">
                    <h3 className="text-white font-montserrat font-bold text-xl mb-6">Featured Courses</h3>
                    <div className="grid grid-cols-1 gap-4">
                      <div className="bg-red-600/20 rounded-lg p-4 border border-red-500/30">
                        <h4 className="text-white font-montserrat font-bold text-lg mb-2">Professional Filmmaking</h4>
                        <p className="text-red-100 text-sm mb-3">Complete training from script to screen with industry professionals.</p>
                        <div className="flex items-center justify-between">
                          <span className="text-red-200 text-sm">20 weeks • In-Person</span>
                          <Link href="/courses/filmmaking" className="text-white hover:text-red-200 text-sm font-bebas tracking-wider">
                            LEARN MORE →
                          </Link>
                        </div>
                      </div>
                      
                      <div className="bg-red-600/20 rounded-lg p-4 border border-red-500/30">
                        <h4 className="text-white font-montserrat font-bold text-lg mb-2">Acting For Film</h4>
                        <p className="text-red-100 text-sm mb-3">Master on-camera acting techniques with professional coaches.</p>
                        <div className="flex items-center justify-between">
                          <span className="text-red-200 text-sm">10 weeks • Online Available</span>
                          <Link href="/courses/acting-for-film" className="text-white hover:text-red-200 text-sm font-bebas tracking-wider">
                            LEARN MORE →
                          </Link>
                        </div>
                      </div>
                      
                      <div className="bg-red-600/20 rounded-lg p-4 border border-red-500/30">
                        <h4 className="text-white font-montserrat font-bold text-lg mb-2">3D Animation</h4>
                        <p className="text-red-100 text-sm mb-3">Create stunning animations using industry-standard software.</p>
                        <div className="flex items-center justify-between">
                          <span className="text-red-200 text-sm">16 weeks • In-Person</span>
                          <Link href="/courses/3d-animation" className="text-white hover:text-red-200 text-sm font-bebas tracking-wider">
                            LEARN MORE →
                          </Link>
                        </div>
                      </div>
                    </div>
                    
                    {/* Bottom CTA */}
                    <div className="mt-6 pt-4 border-t border-red-600/30">
                      <Link 
                        href="/courses" 
                        className="inline-block bg-white text-red-700 px-6 py-2 font-bebas tracking-wider hover:bg-red-100 transition-colors rounded"
                      >
                        VIEW ALL COURSES
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="relative group">
              <button className="text-white hover:text-gray-300 flex items-center space-x-1">
                <span>Resources</span>
                <ChevronDown className="h-4 w-4" />
              </button>
              <div className="absolute top-full left-0 mt-2 w-56 bg-black border border-gray-800 rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                <Link href="/news" className="block px-4 py-2 text-sm hover:bg-gray-800">News & Updates</Link>
                <Link href="/blog" className="block px-4 py-2 text-sm hover:bg-gray-800">Blog</Link>
                <Link href="/events" className="block px-4 py-2 text-sm hover:bg-gray-800">Events</Link>
                <Link href="/gallery" className="block px-4 py-2 text-sm hover:bg-gray-800">Gallery</Link>
                <Link href="/testimonials" className="block px-4 py-2 text-sm hover:bg-gray-800">Testimonials</Link>
                <Link href="/faq" className="block px-4 py-2 text-sm hover:bg-gray-800">FAQ</Link>
                <Link href="/downloads" className="block px-4 py-2 text-sm hover:bg-gray-800">Downloads</Link>
              </div>
            </div>
          </div>

          {/* Logo */}
          <Link href="/" className="flex-shrink-0">
            <Image
              src="/images/dca-logo.png"
              alt="Del York Creative Academy"
              width={120}
              height={60}
              className="h-12 w-auto"
            />
          </Link>

          {/* Right Menu */}
          <div className="hidden lg:flex items-center space-x-8">
            <div className="relative group">
              <button className="text-white hover:text-gray-300 flex items-center space-x-1">
                <span>Community</span>
                <ChevronDown className="h-4 w-4" />
              </button>
              <div className="absolute top-full right-0 mt-2 w-56 bg-black border border-gray-800 rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                <Link href="/instructors" className="block px-4 py-2 text-sm hover:bg-gray-800">Our Instructors</Link>
                <Link href="/alumni" className="block px-4 py-2 text-sm hover:bg-gray-800">Alumni Network</Link>
                <Link href="/student-life" className="block px-4 py-2 text-sm hover:bg-gray-800">Student Life</Link>
                <Link href="/careers" className="block px-4 py-2 text-sm hover:bg-gray-800">Career Services</Link>
                <Link href="/mentorship" className="block px-4 py-2 text-sm hover:bg-gray-800">Mentorship Program</Link>
              </div>
            </div>
            
            <div className="relative group">
              <button className="text-white hover:text-gray-300 flex items-center space-x-1">
                <span>Admissions</span>
                <ChevronDown className="h-4 w-4" />
              </button>
              <div className="absolute top-full right-0 mt-2 w-56 bg-black border border-gray-800 rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                <Link href="/register" className="block px-4 py-2 text-sm hover:bg-gray-800">Apply Now</Link>
                <Link href="/admissions/requirements" className="block px-4 py-2 text-sm hover:bg-gray-800">Requirements</Link>
                <Link href="/admissions/tuition" className="block px-4 py-2 text-sm hover:bg-gray-800">Tuition & Fees</Link>
                <Link href="/admissions/scholarships" className="block px-4 py-2 text-sm hover:bg-gray-800">Scholarships</Link>
                <Link href="/admissions/financial-aid" className="block px-4 py-2 text-sm hover:bg-gray-800">Financial Aid</Link>
                <Link href="/admissions/schedule" className="block px-4 py-2 text-sm hover:bg-gray-800">Class Schedule</Link>
              </div>
            </div>
            
            <Link href="/contact" className="text-white hover:text-gray-300">Contact</Link>
            
            <div className="flex space-x-2">
              <button className="text-white hover:text-gray-300">EN</button>
              <span className="text-gray-500">|</span>
              <button className="text-gray-500 hover:text-white">FR</button>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="text-white hover:text-gray-300"
            >
              {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="lg:hidden bg-black border-t border-gray-800">
            <div className="px-2 pt-2 pb-3 space-y-1 max-h-96 overflow-y-auto">
              <Link href="/about" className="block px-3 py-2 text-white hover:bg-gray-800">About</Link>
              <Link href="/special-programs" className="block px-3 py-2 text-white hover:bg-gray-800">Special Programs</Link>
              <Link href="/courses" className="block px-3 py-2 text-white hover:bg-gray-800">Courses</Link>
              <Link href="/news" className="block px-3 py-2 text-white hover:bg-gray-800">News</Link>
              <Link href="/instructors" className="block px-3 py-2 text-white hover:bg-gray-800">Instructors</Link>
              <Link href="/alumni" className="block px-3 py-2 text-white hover:bg-gray-800">Alumni</Link>
              <Link href="/register" className="block px-3 py-2 text-white hover:bg-gray-800">Register</Link>
              <Link href="/contact" className="block px-3 py-2 text-white hover:bg-gray-800">Contact</Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
